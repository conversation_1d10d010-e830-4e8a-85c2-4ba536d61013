package com.sleekflow.proxy;

import com.sleekflow.proxy.config.GoogleCloudLoggingProperties;
import com.sleekflow.proxy.config.ProxyProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Main application class for the SleekFlow HTTP Proxy Server.
 * <p>
 * This Spring Boot application acts as an HTTP proxy/reverse proxy that:
 * - Accepts incoming HTTP requests on a configurable port
 * - Forwards all requests to a specified destination hostname/URL
 * - Preserves the complete original request (method, headers, body, query parameters)
 * - Supports both HTTP and HTTPS protocols
 * - Includes comprehensive logging and error handling
 * <p>
 * Configuration can be done via:
 * - application.properties file
 * - Environment variables
 * - Command line arguments
 * <p>
 * Example usage:
 * java -jar sleekflow-proxy.jar --proxy.destination.host=http://api.example.com --proxy.server.port=8080
 * <p>
 * Environment variables:
 * PROXY_DESTINATION_HOST=http://api.example.com
 * PROXY_SERVER_PORT=8080
 */
@SpringBootApplication
@EnableConfigurationProperties({ProxyProperties.class, GoogleCloudLoggingProperties.class})
public class SleekFlowProxyApplication implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(SleekFlowProxyApplication.class);

    private final ProxyProperties proxyProperties;

    public SleekFlowProxyApplication(ProxyProperties proxyProperties) {
        this.proxyProperties = proxyProperties;
    }

    public static void main(String[] args) {
        try {
            SpringApplication.run(SleekFlowProxyApplication.class, args);
        } catch (Exception e) {
            logger.error("Failed to start SleekFlow Proxy Application: {}", e.getMessage(), e);
            System.exit(1);
        }
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("=================================================================");
        logger.info("           SleekFlow HTTP Proxy Server Started                  ");
        logger.info("=================================================================");
        logger.info("Server Configuration:");
        logger.info("  Listening Port: {}", proxyProperties.getServerPort());
        logger.info("  Destination Host: {}", proxyProperties.getDestinationHost());
        logger.info("=================================================================");
        logger.info("Proxy server is ready to accept requests at:");
        logger.info("  http://localhost:{}", proxyProperties.getServerPort());
        logger.info("=================================================================");

        // Validate configuration
        validateConfiguration();

        logger.info("All requests will be forwarded to: {}", proxyProperties.getDestinationHost());
        logger.info("Example: http://localhost:{}/api/users -> {}/api/users",
                proxyProperties.getServerPort(), proxyProperties.getDestinationHost());
    }

    /**
     * Validates the proxy configuration to ensure it's properly set up.
     */
    private void validateConfiguration() {
        if (proxyProperties.getDestinationHost() == null || proxyProperties.getDestinationHost().trim().isEmpty()) {
            logger.error("CONFIGURATION ERROR: Destination host is not configured!");
            logger.error("Please set the destination host using one of the following methods:");
            logger.error("1. application.properties: proxy.destination.host=http://example.com");
            logger.error("2. Environment variable: PROXY_DESTINATION_HOST=http://example.com");
            logger.error("3. Command line: --proxy.destination.host=http://example.com");
            throw new IllegalArgumentException("Destination host must be configured");
        }

        if (!proxyProperties.getDestinationHost().startsWith("http://") &&
                !proxyProperties.getDestinationHost().startsWith("https://")) {
            logger.error("CONFIGURATION ERROR: Destination host must start with http:// or https://");
            logger.error("Current value: {}", proxyProperties.getDestinationHost());
            throw new IllegalArgumentException("Destination host must be a valid URL");
        }

        if (proxyProperties.getServerPort() == null || proxyProperties.getServerPort() <= 0 || proxyProperties.getServerPort() > 65535) {
            logger.error("CONFIGURATION ERROR: Server port must be between 1 and 65535");
            logger.error("Current value: {}", proxyProperties.getServerPort());
            throw new IllegalArgumentException("Invalid server port");
        }

        logger.info("Configuration validation passed successfully");
    }
}
