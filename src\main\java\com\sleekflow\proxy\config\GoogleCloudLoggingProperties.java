package com.sleekflow.proxy.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;

/**
 * Configuration properties for Google Cloud Logging integration.
 * 
 * This class handles the configuration for Google Cloud Logging including:
 * - Project ID for Google Cloud
 * - Base64-encoded service account credentials
 * - Enable/disable flag for Google Cloud Logging
 * 
 * Environment variables expected in Azure App Service:
 * - GOOGLE_CLOUD_PROJECT: The Google Cloud project ID
 * - GOOGLE_APPLICATION_CREDENTIALS: Base64-encoded Google service account JSON credentials
 * - GOOGLE_CLOUD_LOGGING_ENABLED: Boolean flag to enable/disable Google Cloud Logging
 */
@ConfigurationProperties(prefix = "google.cloud.logging")
@Validated
public class GoogleCloudLoggingProperties {

    /**
     * Whether Google Cloud Logging is enabled.
     * Defaults to false to ensure application stability if not configured.
     */
    private Boolean enabled = false;

    /**
     * Google Cloud Project ID.
     * This should be set via the GOOGLE_CLOUD_PROJECT environment variable.
     */
    private String projectId;

    /**
     * Base64-encoded Google service account credentials JSON.
     * This should be set via the GOOGLE_APPLICATION_CREDENTIALS environment variable.
     */
    private String credentialsBase64;

    /**
     * Log name for Google Cloud Logging.
     * Defaults to the application name.
     */
    private String logName = "sleekflow-http-proxy";

    /**
     * Resource type for Google Cloud Logging.
     * Defaults to 'global' for general applications.
     */
    private String resourceType = "global";

    /**
     * Whether to enhance log entries with additional metadata.
     * Includes correlation IDs, request information, etc.
     */
    private Boolean enhanceLogEntries = true;

    /**
     * Maximum number of retry attempts for Google Cloud Logging operations.
     */
    private Integer maxRetryAttempts = 3;

    /**
     * Timeout in milliseconds for Google Cloud Logging operations.
     */
    private Long timeoutMs = 5000L;

    // Getters and Setters

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getCredentialsBase64() {
        return credentialsBase64;
    }

    public void setCredentialsBase64(String credentialsBase64) {
        this.credentialsBase64 = credentialsBase64;
    }

    public String getLogName() {
        return logName;
    }

    public void setLogName(String logName) {
        this.logName = logName;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public Boolean getEnhanceLogEntries() {
        return enhanceLogEntries;
    }

    public void setEnhanceLogEntries(Boolean enhanceLogEntries) {
        this.enhanceLogEntries = enhanceLogEntries;
    }

    public Integer getMaxRetryAttempts() {
        return maxRetryAttempts;
    }

    public void setMaxRetryAttempts(Integer maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }

    public Long getTimeoutMs() {
        return timeoutMs;
    }

    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }

    /**
     * Validates that required properties are set when Google Cloud Logging is enabled.
     */
    public boolean isValidConfiguration() {
        if (!Boolean.TRUE.equals(enabled)) {
            return true; // Valid when disabled
        }
        
        return projectId != null && !projectId.trim().isEmpty() &&
               credentialsBase64 != null && !credentialsBase64.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "GoogleCloudLoggingProperties{" +
                "enabled=" + enabled +
                ", projectId='" + projectId + '\'' +
                ", credentialsBase64=" + (credentialsBase64 != null ? "[REDACTED]" : "null") +
                ", logName='" + logName + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", enhanceLogEntries=" + enhanceLogEntries +
                ", maxRetryAttempts=" + maxRetryAttempts +
                ", timeoutMs=" + timeoutMs +
                '}';
    }
}
