package com.sleekflow.proxy.service.impl;

import com.sleekflow.proxy.config.ProxyProperties;
import com.sleekflow.proxy.service.HttpForwardingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

@Service
public class HttpForwardingServiceImpl implements HttpForwardingService {

    private static final Logger logger = LoggerFactory.getLogger(HttpForwardingService.class);

    private final WebClient webClient;
    private final ProxyProperties proxyProperties;

    private static final List<String> EXCLUDED_HEADERS = List.of(
            "host", "connection", "content-length", "transfer-encoding"
    );

    public HttpForwardingServiceImpl(WebClient webClient, ProxyProperties proxyProperties) {
        this.webClient = webClient;
        this.proxyProperties = proxyProperties;
    }

    public Mono<ResponseEntity<byte[]>> forwardRequest(
            HttpMethod method,
            String path,
            HttpHeaders headers,
            byte[] body) {

        try {
            String destinationUrl = buildDestinationUrl(path);

            logger.info("Forwarding {} request to: {}", method, destinationUrl);
            logger.debug("Request headers: {}", headers);

            return webClient
                    .method(method)
                    .uri(destinationUrl)
                    .headers(httpHeaders -> copyHeaders(headers, httpHeaders))
                    .body(body != null ? BodyInserters.fromValue(body) : BodyInserters.empty())
                    .exchangeToMono(response -> {
                        logger.info("Received response with status: {} from {}",
                                response.statusCode(), destinationUrl);

                        return response.toEntity(byte[].class);
                    })
                    .doOnError(error -> {
                        logger.error("Error forwarding request to {}: {}", destinationUrl, error.getMessage());
                    })
                    .onErrorResume(WebClientResponseException.class, ex -> {
                        logger.warn("HTTP error response from {}: {} {}",
                                destinationUrl, ex.getStatusCode(), ex.getMessage());

                        return Mono.just(ResponseEntity
                                .status(ex.getStatusCode())
                                .headers(ex.getHeaders())
                                .body(ex.getResponseBodyAsByteArray()));
                    })
                    .onErrorResume(Exception.class, ex -> {
                        logger.error("Network error forwarding request to {}: {}", destinationUrl, ex.getMessage());

                        return Mono.just(ResponseEntity
                                .status(502)
                                .body(("Proxy Error: " + ex.getMessage()).getBytes()));
                    });

        } catch (Exception e) {
            logger.error("Error building destination URL for path {}: {}", path, e.getMessage());
            return Mono.just(ResponseEntity
                    .status(500)
                    .body(("Proxy Configuration Error: " + e.getMessage()).getBytes()));
        }
    }

    private String buildDestinationUrl(String path) throws URISyntaxException {
        String destinationHost = proxyProperties.getDestinationHost();

        if (destinationHost.endsWith("/")) {
            destinationHost = destinationHost.substring(0, destinationHost.length() - 1);
        }

        if (!path.startsWith("/")) {
            path = "/" + path;
        }

        String fullUrl = destinationHost + path;

        new URI(fullUrl);

        return fullUrl;
    }

    private void copyHeaders(HttpHeaders originalHeaders, HttpHeaders targetHeaders) {
        for (Map.Entry<String, List<String>> entry : originalHeaders.entrySet()) {
            String headerName = entry.getKey().toLowerCase();

            if (!EXCLUDED_HEADERS.contains(headerName)) {
                targetHeaders.addAll(entry.getKey(), entry.getValue());
            }
        }
    }
}
