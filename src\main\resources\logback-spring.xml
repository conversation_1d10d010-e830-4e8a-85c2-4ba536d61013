<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Include Spring Boot's default configuration -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- Console appender with correlation ID support -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} [%X{correlationId:-}] - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Google Cloud Logging appender (conditional) -->
    <springProfile name="!test">
        <appender name="GOOGLE_CLOUD" class="com.sleekflow.proxy.logging.GoogleCloudLoggingAppender">
            <!-- This appender will be initialized by Spring context -->
        </appender>
    </springProfile>

    <!-- File appender for local development -->
    <springProfile name="development,local">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/sleekflow-proxy.log</file>
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} [%X{correlationId:-}] - %msg%n</pattern>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/sleekflow-proxy.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>10MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>1GB</totalSizeCap>
            </rollingPolicy>
        </appender>
    </springProfile>

    <!-- Root logger configuration -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        
        <!-- Add Google Cloud Logging for non-test profiles -->
        <springProfile name="!test">
            <appender-ref ref="GOOGLE_CLOUD"/>
        </springProfile>
        
        <!-- Add file logging for development -->
        <springProfile name="development,local">
            <appender-ref ref="FILE"/>
        </springProfile>
    </root>

    <!-- Application-specific loggers -->
    <logger name="com.sleekflow.proxy" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        
        <springProfile name="!test">
            <appender-ref ref="GOOGLE_CLOUD"/>
        </springProfile>
        
        <springProfile name="development,local">
            <appender-ref ref="FILE"/>
        </springProfile>
    </logger>

    <!-- Development profile - more verbose logging -->
    <springProfile name="development">
        <logger name="com.sleekflow.proxy" level="DEBUG"/>
        <logger name="org.springframework.web.reactive.function.client" level="DEBUG"/>
        <logger name="org.springframework.web.client" level="DEBUG"/>
    </springProfile>

    <!-- Production profile - optimized logging -->
    <springProfile name="production">
        <logger name="com.sleekflow.proxy" level="INFO"/>
        <logger name="org.springframework.web.reactive.function.client" level="WARN"/>
        <logger name="org.springframework.web" level="WARN"/>
        <logger name="org.apache.http" level="WARN"/>
    </springProfile>

    <!-- Staging profile -->
    <springProfile name="staging">
        <logger name="com.sleekflow.proxy" level="INFO"/>
        <logger name="org.springframework.web.reactive.function.client" level="INFO"/>
    </springProfile>

    <!-- Reduce noise from third-party libraries -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="io.netty" level="WARN"/>
    <logger name="reactor.netty" level="WARN"/>
    
    <!-- Google Cloud Logging specific loggers -->
    <logger name="com.google.cloud" level="WARN"/>
    <logger name="io.grpc" level="WARN"/>
    
    <!-- Prevent logging loops -->
    <logger name="com.sleekflow.proxy.logging" level="WARN"/>
    <logger name="com.sleekflow.proxy.service.GoogleCloudLoggingService" level="INFO"/>

</configuration>
