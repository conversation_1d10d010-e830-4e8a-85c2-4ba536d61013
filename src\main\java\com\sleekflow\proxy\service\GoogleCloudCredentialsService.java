package com.sleekflow.proxy.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.sleekflow.proxy.config.GoogleCloudLoggingProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collections;

/**
 * Service for handling Google Cloud credentials decoding and authentication.
 * 
 * This service handles:
 * - Decoding Base64-encoded service account credentials
 * - Creating GoogleCredentials objects for authentication
 * - Validating credential format and content
 * - Graceful error handling to maintain application stability
 */
@Service
public class GoogleCloudCredentialsService {

    private static final Logger logger = LoggerFactory.getLogger(GoogleCloudCredentialsService.class);
    
    private final ObjectMapper objectMapper;
    private final GoogleCloudLoggingProperties properties;
    
    // Cache for decoded credentials to avoid repeated decoding
    private volatile GoogleCredentials cachedCredentials;
    private volatile String cachedCredentialsHash;

    public GoogleCloudCredentialsService(GoogleCloudLoggingProperties properties) {
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Gets Google Cloud credentials from the configured Base64-encoded string.
     * 
     * @return GoogleCredentials object or null if credentials cannot be obtained
     */
    public GoogleCredentials getCredentials() {
        if (!Boolean.TRUE.equals(properties.getEnabled())) {
            logger.debug("Google Cloud Logging is disabled, skipping credentials loading");
            return null;
        }

        String credentialsBase64 = properties.getCredentialsBase64();
        if (credentialsBase64 == null || credentialsBase64.trim().isEmpty()) {
            logger.warn("Google Cloud Logging is enabled but no credentials provided. " +
                       "Set GOOGLE_APPLICATION_CREDENTIALS environment variable with Base64-encoded service account JSON.");
            return null;
        }

        // Check if we can use cached credentials
        String currentHash = Integer.toString(credentialsBase64.hashCode());
        if (cachedCredentials != null && currentHash.equals(cachedCredentialsHash)) {
            logger.debug("Using cached Google Cloud credentials");
            return cachedCredentials;
        }

        try {
            // Decode Base64 credentials
            byte[] decodedCredentials = Base64.getDecoder().decode(credentialsBase64.trim());
            String credentialsJson = new String(decodedCredentials, StandardCharsets.UTF_8);
            
            // Validate JSON format
            if (!isValidServiceAccountJson(credentialsJson)) {
                logger.error("Invalid service account JSON format in credentials");
                return null;
            }

            // Create credentials from JSON
            ByteArrayInputStream credentialsStream = new ByteArrayInputStream(decodedCredentials);
            GoogleCredentials credentials = ServiceAccountCredentials.fromStream(credentialsStream)
                    .createScoped(Collections.singletonList("https://www.googleapis.com/auth/logging.write"));

            // Cache the credentials
            cachedCredentials = credentials;
            cachedCredentialsHash = currentHash;
            
            logger.info("Successfully loaded Google Cloud credentials");
            return credentials;

        } catch (IllegalArgumentException e) {
            logger.error("Failed to decode Base64 credentials: {}. " +
                        "Ensure GOOGLE_APPLICATION_CREDENTIALS contains valid Base64-encoded JSON.", e.getMessage());
            return null;
        } catch (IOException e) {
            logger.error("Failed to create Google Cloud credentials from JSON: {}. " +
                        "Ensure the service account JSON is valid.", e.getMessage());
            return null;
        } catch (Exception e) {
            logger.error("Unexpected error loading Google Cloud credentials: {}. " +
                        "Application will continue with console logging only.", e.getMessage());
            return null;
        }
    }

    /**
     * Validates that the provided JSON string is a valid service account JSON.
     * 
     * @param json The JSON string to validate
     * @return true if valid service account JSON, false otherwise
     */
    private boolean isValidServiceAccountJson(String json) {
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            
            // Check for required fields in service account JSON
            return jsonNode.has("type") && 
                   "service_account".equals(jsonNode.get("type").asText()) &&
                   jsonNode.has("project_id") &&
                   jsonNode.has("private_key_id") &&
                   jsonNode.has("private_key") &&
                   jsonNode.has("client_email") &&
                   jsonNode.has("client_id");
                   
        } catch (Exception e) {
            logger.debug("JSON validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Extracts the project ID from the service account credentials.
     * 
     * @return Project ID from credentials or configured project ID
     */
    public String getProjectId() {
        // First try to get from configuration
        if (properties.getProjectId() != null && !properties.getProjectId().trim().isEmpty()) {
            return properties.getProjectId();
        }

        // Try to extract from credentials
        String credentialsBase64 = properties.getCredentialsBase64();
        if (credentialsBase64 != null && !credentialsBase64.trim().isEmpty()) {
            try {
                byte[] decodedCredentials = Base64.getDecoder().decode(credentialsBase64.trim());
                String credentialsJson = new String(decodedCredentials, StandardCharsets.UTF_8);
                JsonNode jsonNode = objectMapper.readTree(credentialsJson);
                
                if (jsonNode.has("project_id")) {
                    String projectId = jsonNode.get("project_id").asText();
                    logger.debug("Extracted project ID from credentials: {}", projectId);
                    return projectId;
                }
            } catch (Exception e) {
                logger.debug("Failed to extract project ID from credentials: {}", e.getMessage());
            }
        }

        logger.warn("No project ID found in configuration or credentials. " +
                   "Set GOOGLE_CLOUD_PROJECT environment variable.");
        return null;
    }

    /**
     * Checks if Google Cloud Logging is properly configured and credentials are available.
     * 
     * @return true if properly configured, false otherwise
     */
    public boolean isConfigured() {
        return Boolean.TRUE.equals(properties.getEnabled()) && 
               getCredentials() != null && 
               getProjectId() != null;
    }

    /**
     * Clears cached credentials (useful for testing or credential rotation).
     */
    public void clearCache() {
        cachedCredentials = null;
        cachedCredentialsHash = null;
        logger.debug("Google Cloud credentials cache cleared");
    }
}
