package com.sleekflow.proxy.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.UUID;

/**
 * Filter that logs detailed information about incoming requests and outgoing responses.
 * Adds correlation IDs to track requests through the proxy.
 */
@Component
@Order(1)
public class RequestLoggingFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(RequestLoggingFilter.class);
    private static final String CORRELATION_ID_HEADER = "X-Correlation-ID";
    private static final String CORRELATION_ID_MDC_KEY = "correlationId";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        long startTime = System.currentTimeMillis();
        
        // Generate or extract correlation ID
        String correlationId = getOrGenerateCorrelationId(request);
        
        // Add correlation ID to MDC for logging
        MDC.put(CORRELATION_ID_MDC_KEY, correlationId);
        
        // Add correlation ID to response headers
        response.setHeader(CORRELATION_ID_HEADER, correlationId);
        
        try {
            // Log incoming request
            logIncomingRequest(request, correlationId);
            
            // Process the request
            filterChain.doFilter(request, response);
            
            // Log outgoing response
            logOutgoingResponse(request, response, correlationId, startTime);
            
        } finally {
            // Clean up MDC
            MDC.remove(CORRELATION_ID_MDC_KEY);
        }
    }

    /**
     * Gets correlation ID from request header or generates a new one.
     */
    private String getOrGenerateCorrelationId(HttpServletRequest request) {
        String correlationId = request.getHeader(CORRELATION_ID_HEADER);
        if (correlationId == null || correlationId.trim().isEmpty()) {
            correlationId = UUID.randomUUID().toString();
        }
        return correlationId;
    }

    /**
     * Logs detailed information about the incoming request.
     */
    private void logIncomingRequest(HttpServletRequest request, String correlationId) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("Referer");
        
        logger.info("INCOMING REQUEST [{}] {} {} from {} - User-Agent: {} - Referer: {}",
                correlationId,
                request.getMethod(),
                getFullRequestUrl(request),
                clientIp,
                userAgent != null ? userAgent : "N/A",
                referer != null ? referer : "N/A");
        
        // Log content type and length for requests with body
        String contentType = request.getContentType();
        int contentLength = request.getContentLength();
        if (contentType != null || contentLength > 0) {
            logger.debug("REQUEST BODY [{}] Content-Type: {} - Content-Length: {}",
                    correlationId,
                    contentType != null ? contentType : "N/A",
                    contentLength > 0 ? contentLength : "N/A");
        }
    }

    /**
     * Logs information about the outgoing response.
     */
    private void logOutgoingResponse(HttpServletRequest request, HttpServletResponse response, 
                                   String correlationId, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        
        logger.info("OUTGOING RESPONSE [{}] {} {} - Status: {} - Duration: {}ms",
                correlationId,
                request.getMethod(),
                getFullRequestUrl(request),
                response.getStatus(),
                duration);
        
        // Log warning for slow requests
        if (duration > 5000) { // 5 seconds
            logger.warn("SLOW REQUEST [{}] Request took {}ms to complete", correlationId, duration);
        }
        
        // Log error for failed requests
        if (response.getStatus() >= 400) {
            logger.warn("FAILED REQUEST [{}] Request failed with status {}", correlationId, response.getStatus());
        }
    }

    /**
     * Extracts the client's IP address, considering proxy headers.
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            // X-Forwarded-For can contain multiple IPs, take the first one
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * Constructs the full request URL including query parameters.
     */
    private String getFullRequestUrl(HttpServletRequest request) {
        StringBuilder url = new StringBuilder(request.getRequestURI());
        String queryString = request.getQueryString();
        if (queryString != null && !queryString.isEmpty()) {
            url.append("?").append(queryString);
        }
        return url.toString();
    }
}
