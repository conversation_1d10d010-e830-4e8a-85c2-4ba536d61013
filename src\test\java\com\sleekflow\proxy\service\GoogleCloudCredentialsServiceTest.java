package com.sleekflow.proxy.service;

import com.sleekflow.proxy.config.GoogleCloudLoggingProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class GoogleCloudCredentialsServiceTest {

    private GoogleCloudCredentialsService credentialsService;
    private GoogleCloudLoggingProperties properties;

    // Valid service account JSON for testing
    private static final String VALID_SERVICE_ACCOUNT_JSON = """
        {
          "type": "service_account",
          "project_id": "test-project-123",
          "private_key_id": "key-id-123",
          "private_key": "-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\\n-----END PRIVATE KEY-----\\n",
          "client_email": "<EMAIL>",
          "client_id": "123456789012345678901",
          "auth_uri": "https://accounts.google.com/o/oauth2/auth",
          "token_uri": "https://oauth2.googleapis.com/token"
        }
        """;

    private static final String INVALID_JSON = "{ invalid json }";

    @BeforeEach
    void setUp() {
        properties = new GoogleCloudLoggingProperties();
        credentialsService = new GoogleCloudCredentialsService(properties);
    }

    @Test
    void testGetCredentials_WhenDisabled_ReturnsNull() {
        // Given
        properties.setEnabled(false);
        properties.setCredentialsBase64(Base64.getEncoder().encodeToString(VALID_SERVICE_ACCOUNT_JSON.getBytes()));

        // When
        var credentials = credentialsService.getCredentials();

        // Then
        assertNull(credentials);
    }

    @Test
    void testGetCredentials_WhenNoCredentials_ReturnsNull() {
        // Given
        properties.setEnabled(true);
        properties.setCredentialsBase64(null);

        // When
        var credentials = credentialsService.getCredentials();

        // Then
        assertNull(credentials);
    }

    @Test
    void testGetCredentials_WhenEmptyCredentials_ReturnsNull() {
        // Given
        properties.setEnabled(true);
        properties.setCredentialsBase64("");

        // When
        var credentials = credentialsService.getCredentials();

        // Then
        assertNull(credentials);
    }

    @Test
    void testGetCredentials_WhenInvalidBase64_ReturnsNull() {
        // Given
        properties.setEnabled(true);
        properties.setCredentialsBase64("invalid-base64!");

        // When
        var credentials = credentialsService.getCredentials();

        // Then
        assertNull(credentials);
    }

    @Test
    void testGetCredentials_WhenInvalidJson_ReturnsNull() {
        // Given
        properties.setEnabled(true);
        properties.setCredentialsBase64(Base64.getEncoder().encodeToString(INVALID_JSON.getBytes()));

        // When
        var credentials = credentialsService.getCredentials();

        // Then
        assertNull(credentials);
    }

    @Test
    void testGetProjectId_FromConfiguration() {
        // Given
        properties.setProjectId("config-project-id");

        // When
        String projectId = credentialsService.getProjectId();

        // Then
        assertEquals("config-project-id", projectId);
    }

    @Test
    void testGetProjectId_FromCredentials() {
        // Given
        properties.setProjectId(null);
        properties.setCredentialsBase64(Base64.getEncoder().encodeToString(VALID_SERVICE_ACCOUNT_JSON.getBytes()));

        // When
        String projectId = credentialsService.getProjectId();

        // Then
        assertEquals("test-project-123", projectId);
    }

    @Test
    void testGetProjectId_WhenNeitherAvailable_ReturnsNull() {
        // Given
        properties.setProjectId(null);
        properties.setCredentialsBase64(null);

        // When
        String projectId = credentialsService.getProjectId();

        // Then
        assertNull(projectId);
    }

    @Test
    void testIsConfigured_WhenDisabled_ReturnsFalse() {
        // Given
        properties.setEnabled(false);

        // When
        boolean configured = credentialsService.isConfigured();

        // Then
        assertFalse(configured);
    }

    @Test
    void testIsConfigured_WhenNoCredentials_ReturnsFalse() {
        // Given
        properties.setEnabled(true);
        properties.setCredentialsBase64(null);

        // When
        boolean configured = credentialsService.isConfigured();

        // Then
        assertFalse(configured);
    }

    @Test
    void testClearCache() {
        // Given
        properties.setEnabled(true);
        properties.setCredentialsBase64(Base64.getEncoder().encodeToString(VALID_SERVICE_ACCOUNT_JSON.getBytes()));
        
        // Get credentials to populate cache
        credentialsService.getCredentials();

        // When
        credentialsService.clearCache();

        // Then - should not throw exception
        assertDoesNotThrow(() -> credentialsService.clearCache());
    }

    @Test
    void testCredentialsCaching() {
        // Given
        properties.setEnabled(true);
        String base64Credentials = Base64.getEncoder().encodeToString(VALID_SERVICE_ACCOUNT_JSON.getBytes());
        properties.setCredentialsBase64(base64Credentials);

        // When - call multiple times
        var credentials1 = credentialsService.getCredentials();
        var credentials2 = credentialsService.getCredentials();

        // Then - should return same instance (cached)
        assertSame(credentials1, credentials2);
    }

    @Test
    void testValidConfiguration() {
        // Given
        properties.setEnabled(true);
        properties.setProjectId("test-project");
        properties.setCredentialsBase64(Base64.getEncoder().encodeToString(VALID_SERVICE_ACCOUNT_JSON.getBytes()));

        // When
        boolean valid = properties.isValidConfiguration();

        // Then
        assertTrue(valid);
    }

    @Test
    void testInvalidConfiguration_MissingProjectId() {
        // Given
        properties.setEnabled(true);
        properties.setProjectId(null);
        properties.setCredentialsBase64(Base64.getEncoder().encodeToString(VALID_SERVICE_ACCOUNT_JSON.getBytes()));

        // When
        boolean valid = properties.isValidConfiguration();

        // Then
        assertFalse(valid);
    }

    @Test
    void testInvalidConfiguration_MissingCredentials() {
        // Given
        properties.setEnabled(true);
        properties.setProjectId("test-project");
        properties.setCredentialsBase64(null);

        // When
        boolean valid = properties.isValidConfiguration();

        // Then
        assertFalse(valid);
    }

    @Test
    void testConfigurationValidWhenDisabled() {
        // Given
        properties.setEnabled(false);
        properties.setProjectId(null);
        properties.setCredentialsBase64(null);

        // When
        boolean valid = properties.isValidConfiguration();

        // Then
        assertTrue(valid); // Should be valid when disabled
    }
}
