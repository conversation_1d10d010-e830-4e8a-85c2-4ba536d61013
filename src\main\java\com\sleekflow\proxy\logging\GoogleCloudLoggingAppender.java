package com.sleekflow.proxy.logging;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import com.sleekflow.proxy.service.GoogleCloudLoggingService;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * Custom Logback appender that integrates with Google Cloud Logging service.
 * 
 * This appender:
 * - Forwards log events to Google Cloud Logging service
 * - Maintains graceful fallback to console logging
 * - Handles Spring context integration
 * - Provides non-blocking logging operations
 */
public class GoogleCloudLoggingAppender extends AppenderBase<ILoggingEvent> implements ApplicationContextAware {

    private ApplicationContext applicationContext;
    private GoogleCloudLoggingService googleCloudLoggingService;
    private volatile boolean initialized = false;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void start() {
        super.start();
        initializeService();
    }

    /**
     * Initializes the Google Cloud Logging service from Spring context.
     */
    private void initializeService() {
        if (applicationContext != null) {
            try {
                googleCloudLoggingService = applicationContext.getBean(GoogleCloudLoggingService.class);
                initialized = true;
                addInfo("Google Cloud Logging appender initialized successfully");
            } catch (Exception e) {
                addWarn("Failed to initialize Google Cloud Logging appender: " + e.getMessage());
                initialized = false;
            }
        } else {
            addWarn("Application context not available for Google Cloud Logging appender");
            initialized = false;
        }
    }

    @Override
    protected void append(ILoggingEvent event) {
        if (!initialized || googleCloudLoggingService == null) {
            // Skip if not properly initialized
            return;
        }

        try {
            String level = event.getLevel().toString();
            String message = event.getFormattedMessage();
            Throwable throwable = event.getThrowableProxy() != null ? 
                new RuntimeException(event.getThrowableProxy().getMessage()) : null;

            // Forward to Google Cloud Logging service asynchronously
            googleCloudLoggingService.logAsync(level, message, throwable);

        } catch (Exception e) {
            // Avoid logging errors that could cause infinite loops
            addWarn("Error in Google Cloud Logging appender: " + e.getMessage());
        }
    }

    @Override
    public void stop() {
        super.stop();
        initialized = false;
    }
}
