package com.sleekflow.proxy.service;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.MonitoredResource;
import com.google.cloud.logging.LogEntry;
import com.google.cloud.logging.Logging;
import com.google.cloud.logging.LoggingOptions;
import com.google.cloud.logging.Payload;
import com.google.cloud.logging.Severity;
import com.sleekflow.proxy.config.GoogleCloudLoggingProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Service for Google Cloud Logging operations with graceful fallback.
 * 
 * This service provides:
 * - Asynchronous logging to Google Cloud Logging
 * - Graceful fallback to console logging on failures
 * - Enhanced log entries with correlation IDs and metadata
 * - Connection health monitoring
 * - Automatic retry mechanisms
 */
@Service
public class GoogleCloudLoggingService {

    private static final Logger logger = LoggerFactory.getLogger(GoogleCloudLoggingService.class);
    
    private final GoogleCloudLoggingProperties properties;
    private final GoogleCloudCredentialsService credentialsService;
    
    private volatile Logging logging;
    private volatile MonitoredResource resource;
    private volatile boolean isHealthy = false;
    private volatile String lastError;
    
    private ExecutorService executorService;

    public GoogleCloudLoggingService(GoogleCloudLoggingProperties properties, 
                                   GoogleCloudCredentialsService credentialsService) {
        this.properties = properties;
        this.credentialsService = credentialsService;
    }

    @PostConstruct
    public void initialize() {
        if (!Boolean.TRUE.equals(properties.getEnabled())) {
            logger.info("Google Cloud Logging is disabled");
            return;
        }

        logger.info("Initializing Google Cloud Logging service...");
        
        // Create executor for async operations
        executorService = Executors.newFixedThreadPool(2, r -> {
            Thread t = new Thread(r, "google-cloud-logging");
            t.setDaemon(true);
            return t;
        });

        // Initialize Google Cloud Logging client
        initializeLoggingClient();
    }

    @PreDestroy
    public void shutdown() {
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (logging != null) {
            try {
                logging.close();
            } catch (Exception e) {
                logger.debug("Error closing Google Cloud Logging client: {}", e.getMessage());
            }
        }
    }

    /**
     * Initializes the Google Cloud Logging client.
     */
    private void initializeLoggingClient() {
        try {
            GoogleCredentials credentials = credentialsService.getCredentials();
            String projectId = credentialsService.getProjectId();

            if (credentials == null || projectId == null) {
                logger.warn("Google Cloud Logging initialization failed: missing credentials or project ID");
                isHealthy = false;
                lastError = "Missing credentials or project ID";
                return;
            }

            // Build logging client
            LoggingOptions.Builder optionsBuilder = LoggingOptions.newBuilder()
                    .setProjectId(projectId)
                    .setCredentials(credentials);

            logging = optionsBuilder.build().getService();

            // Create monitored resource
            resource = MonitoredResource.newBuilder(properties.getResourceType())
                    .addLabel("project_id", projectId)
                    .build();

            // Test connection
            testConnection();

            isHealthy = true;
            lastError = null;
            logger.info("Google Cloud Logging initialized successfully for project: {}", projectId);

        } catch (Exception e) {
            isHealthy = false;
            lastError = e.getMessage();
            logger.error("Failed to initialize Google Cloud Logging: {}. " +
                        "Application will continue with console logging only.", e.getMessage());
        }
    }

    /**
     * Tests the connection to Google Cloud Logging.
     */
    private void testConnection() {
        if (logging == null) {
            throw new IllegalStateException("Logging client not initialized");
        }

        try {
            // Create a test log entry
            LogEntry testEntry = LogEntry.newBuilder(Payload.StringPayload.of("Google Cloud Logging connection test"))
                    .setSeverity(Severity.INFO)
                    .setLogName(properties.getLogName())
                    .setResource(resource)
                    .setTimestamp(Instant.now().toEpochMilli())
                    .build();

            logging.write(Collections.singletonList(testEntry));
            logger.debug("Google Cloud Logging connection test successful");

        } catch (Exception e) {
            throw new RuntimeException("Connection test failed: " + e.getMessage(), e);
        }
    }

    /**
     * Logs a message to Google Cloud Logging asynchronously.
     * Falls back to console logging if Google Cloud Logging fails.
     * 
     * @param level The log level
     * @param message The log message
     * @param throwable Optional throwable for error logs
     */
    public void logAsync(String level, String message, Throwable throwable) {
        if (!isHealthy || logging == null) {
            // Fallback to console logging
            logToConsole(level, message, throwable);
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                logToGoogleCloud(level, message, throwable);
            } catch (Exception e) {
                logger.debug("Failed to log to Google Cloud: {}. Using console fallback.", e.getMessage());
                logToConsole(level, message, throwable);
                
                // Mark as unhealthy after failure
                isHealthy = false;
                lastError = e.getMessage();
            }
        }, executorService);
    }

    /**
     * Logs a message to Google Cloud Logging.
     */
    private void logToGoogleCloud(String level, String message, Throwable throwable) {
        Severity severity = mapLogLevelToSeverity(level);
        
        // Build log entry
        LogEntry.Builder entryBuilder = LogEntry.newBuilder(Payload.StringPayload.of(message))
                .setSeverity(severity)
                .setLogName(properties.getLogName())
                .setResource(resource)
                .setTimestamp(Instant.now().toEpochMilli());

        // Add enhanced metadata if enabled
        if (Boolean.TRUE.equals(properties.getEnhanceLogEntries())) {
            Map<String, String> labels = new HashMap<>();
            
            // Add correlation ID from MDC
            String correlationId = MDC.get("correlationId");
            if (correlationId != null) {
                labels.put("correlation_id", correlationId);
            }
            
            // Add application metadata
            labels.put("application", "sleekflow-http-proxy");
            labels.put("environment", System.getProperty("spring.profiles.active", "default"));
            
            if (!labels.isEmpty()) {
                entryBuilder.setLabels(labels);
            }
        }

        // Add exception details if present
        if (throwable != null) {
            Map<String, Object> jsonPayload = new HashMap<>();
            jsonPayload.put("message", message);
            jsonPayload.put("exception_class", throwable.getClass().getName());
            jsonPayload.put("exception_message", throwable.getMessage());
            jsonPayload.put("stack_trace", getStackTrace(throwable));
            
            entryBuilder = LogEntry.newBuilder(Payload.JsonPayload.of(jsonPayload))
                    .setSeverity(severity)
                    .setLogName(properties.getLogName())
                    .setResource(resource)
                    .setTimestamp(Instant.now().toEpochMilli());
        }

        LogEntry logEntry = entryBuilder.build();
        logging.write(Collections.singletonList(logEntry));
    }

    /**
     * Fallback console logging.
     */
    private void logToConsole(String level, String message, Throwable throwable) {
        switch (level.toUpperCase()) {
            case "ERROR":
                if (throwable != null) {
                    logger.error(message, throwable);
                } else {
                    logger.error(message);
                }
                break;
            case "WARN":
                if (throwable != null) {
                    logger.warn(message, throwable);
                } else {
                    logger.warn(message);
                }
                break;
            case "DEBUG":
                if (throwable != null) {
                    logger.debug(message, throwable);
                } else {
                    logger.debug(message);
                }
                break;
            default:
                if (throwable != null) {
                    logger.info(message, throwable);
                } else {
                    logger.info(message);
                }
                break;
        }
    }

    /**
     * Maps log level string to Google Cloud Logging Severity.
     */
    private Severity mapLogLevelToSeverity(String level) {
        switch (level.toUpperCase()) {
            case "ERROR":
                return Severity.ERROR;
            case "WARN":
                return Severity.WARNING;
            case "DEBUG":
                return Severity.DEBUG;
            case "TRACE":
                return Severity.DEBUG;
            default:
                return Severity.INFO;
        }
    }

    /**
     * Extracts stack trace from throwable.
     */
    private String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(throwable.toString()).append("\n");
        
        for (StackTraceElement element : throwable.getStackTrace()) {
            sb.append("\tat ").append(element.toString()).append("\n");
        }
        
        return sb.toString();
    }

    /**
     * Gets the health status of Google Cloud Logging.
     */
    public boolean isHealthy() {
        return isHealthy;
    }

    /**
     * Gets the last error message.
     */
    public String getLastError() {
        return lastError;
    }

    /**
     * Attempts to reinitialize the Google Cloud Logging client.
     */
    public void reinitialize() {
        logger.info("Reinitializing Google Cloud Logging service...");
        initializeLoggingClient();
    }
}
