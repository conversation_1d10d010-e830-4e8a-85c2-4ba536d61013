spring:
  application:
    name: sleekflow-http-proxy
  main:
    banner-mode: console

proxy:
  followRedirects: ${PROXY_FOLLOW_REDIRECTS:true}
  maxRedirects: ${PROXY_MAX_REDIRECTS:5}

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized