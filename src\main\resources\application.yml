spring:
  application:
    name: sleekflow-http-proxy
  main:
    banner-mode: console

proxy:
  followRedirects: ${PROXY_FOLLOW_REDIRECTS:true}
  maxRedirects: ${PROXY_MAX_REDIRECTS:5}

# Google Cloud Logging Configuration
google:
  cloud:
    logging:
      enabled: ${GOOGLE_CLOUD_LOGGING_ENABLED:false}
      projectId: ${GOOGLE_CLOUD_PROJECT:}
      credentialsBase64: ${GOOGLE_APPLICATION_CREDENTIALS:}
      logName: ${GOOGLE_CLOUD_LOG_NAME:sleekflow-http-proxy}
      resourceType: ${GOOGLE_CLOUD_RESOURCE_TYPE:global}
      enhanceLogEntries: ${GOOGLE_CLOUD_ENHANCE_LOGS:true}
      maxRetryAttempts: ${GOOGLE_CLOUD_MAX_RETRIES:3}
      timeoutMs: ${GOOGLE_CLOUD_TIMEOUT_MS:5000}

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} [%X{correlationId:-}] - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,google-cloud-logging
  endpoint:
    health:
      show-details: when-authorized