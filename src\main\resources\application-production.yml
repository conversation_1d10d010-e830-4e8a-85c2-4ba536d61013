proxy:
  destinationHost: ${PROXY_DESTINATION_HOST:https://api.sleekflow.io}
  serverPort: ${PROXY_SERVER_PORT:8080}

server:
  port: ${proxy.serverPort}

# Enable Google Cloud Logging in production
google:
  cloud:
    logging:
      enabled: ${GOOGLE_CLOUD_LOGGING_ENABLED:true}
      logName: sleekflow-http-proxy-prod
      resourceType: gce_instance

logging:
  level:
    com.sleekflow.proxy: INFO
    org.springframework.web.reactive.function.client: INFO
    org.springframework.web: INFO

management:
  endpoint:
    health:
  show-details: when-authorized
  endpoints:
    web:
      exposure:
        include: health,info,metrics,google-cloud-logging