# SleekFlow HTTP Proxy Server

A high-performance HTTP proxy/reverse proxy server built with Spring Boot 3 that forwards all incoming requests to a configurable destination server while preserving the complete original request.

## Features

- **Complete Request Preservation**: Forwards HTTP method, headers, body, query parameters, and authentication information
- **Protocol Support**: Supports both HTTP and HTTPS protocols
- **Configurable**: Destination hostname and listening port are fully configurable
- **Comprehensive Logging**: Detailed request tracking with correlation IDs
- **Error Handling**: Robust error handling for network failures and invalid requests
- **Health Monitoring**: Built-in health check endpoints
- **Performance**: Optimized with connection pooling and reactive programming

## Quick Start

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Build and Run

1. **Clone and build the project:**
   ```bash
   git clone <repository-url>
   cd SleekFlow.Proxy
   mvn clean package
   ```

2. **Run with default configuration:**
   ```bash
   java -jar target/http-proxy-1.0.0.jar --proxy.destination.host=http://httpbin.org
   ```

3. **Test the proxy:**
   ```bash
   curl http://localhost:8080/get
   # This will forward the request to http://httpbin.org/get
   ```

## Configuration

The proxy can be configured using multiple methods (in order of precedence):

### 1. Command Line Arguments
```bash
java -jar http-proxy-1.0.0.jar \
  --proxy.destination.host=http://api.example.com \
  --proxy.server.port=8080
```

### 2. Environment Variables
```bash
export PROXY_DESTINATION_HOST=http://api.example.com
export PROXY_SERVER_PORT=8080
java -jar http-proxy-1.0.0.jar
```

### 3. Application Properties
Edit `src/main/resources/application.properties`:
```properties
proxy.destination.host=http://api.example.com
proxy.server.port=8080
```

### Configuration Options

| Property | Environment Variable | Default | Description |
|----------|---------------------|---------|-------------|
| `proxy.destination.host` | `PROXY_DESTINATION_HOST` | `http://httpbin.org` | Destination server URL |
| `proxy.server.port` | `PROXY_SERVER_PORT` | `8080` | Proxy listening port |
| `proxy.connection-timeout-ms` | `PROXY_CONNECTION_TIMEOUT_MS` | `30000` | Connection timeout (ms) |
| `proxy.read-timeout-ms` | `PROXY_READ_TIMEOUT_MS` | `60000` | Read timeout (ms) |
| `proxy.follow-redirects` | `PROXY_FOLLOW_REDIRECTS` | `true` | Follow HTTP redirects |
| `proxy.max-redirects` | `PROXY_MAX_REDIRECTS` | `5` | Maximum redirects to follow |

## Usage Examples

### Basic HTTP Forwarding
```bash
# Start proxy forwarding to httpbin.org
java -jar http-proxy-1.0.0.jar --proxy.destination.host=http://httpbin.org

# Test GET request
curl http://localhost:8080/get

# Test POST request with JSON body
curl -X POST http://localhost:8080/post \
  -H "Content-Type: application/json" \
  -d '{"key": "value"}'
```

### API Gateway Scenario
```bash
# Forward to your API server
java -jar http-proxy-1.0.0.jar --proxy.destination.host=http://api.mycompany.com

# All requests are forwarded:
curl http://localhost:8080/api/users          # -> http://api.mycompany.com/api/users
curl http://localhost:8080/api/orders/123     # -> http://api.mycompany.com/api/orders/123
```

### HTTPS Destination
```bash
# Forward to HTTPS endpoint
java -jar http-proxy-1.0.0.jar --proxy.destination.host=https://api.github.com

curl http://localhost:8080/user              # -> https://api.github.com/user
```

## Monitoring and Health Checks

The proxy provides built-in monitoring endpoints:

### Health Check
```bash
curl http://localhost:8080/_proxy/health
```
Response:
```json
{
  "status": "UP",
  "timestamp": "2024-01-01T12:00:00Z",
  "uptime": 300000
}
```

### Detailed Status
```bash
curl http://localhost:8080/_proxy/status
```
Response:
```json
{
  "status": "UP",
  "timestamp": "2024-01-01T12:00:00Z",
  "startTime": "2024-01-01T11:55:00Z",
  "uptime": 300000,
  "configuration": {
    "destinationHost": "http://httpbin.org",
    "serverPort": 8080,
    "connectionTimeoutMs": 30000,
    "readTimeoutMs": 60000,
    "followRedirects": true,
    "maxRedirects": 5
  }
}
```

## Logging

The proxy provides comprehensive logging with correlation IDs for request tracking and supports both console and Google Cloud Logging:

```
2024-01-01 12:00:00 - INCOMING REQUEST [abc-123] GET /api/users from *************
2024-01-01 12:00:00 - Forwarding GET request to: http://api.example.com/api/users
2024-01-01 12:00:01 - OUTGOING RESPONSE [abc-123] GET /api/users - Status: 200 - Duration: 150ms
```

### Log Levels
- `INFO`: Request/response summaries
- `DEBUG`: Detailed headers and body information
- `WARN`: Slow requests and error responses
- `ERROR`: Network failures and configuration errors

### Google Cloud Logging Integration

The application supports centralized logging to Google Cloud Logging with graceful fallback to console logging.

#### Features
- **Asynchronous Logging**: Non-blocking log operations to maintain application performance
- **Graceful Fallback**: Automatically falls back to console logging if Google Cloud Logging fails
- **Enhanced Metadata**: Includes correlation IDs, application context, and structured data
- **Health Monitoring**: Built-in health checks for logging service status
- **Credential Security**: Supports Base64-encoded service account credentials

#### Configuration

Set the following environment variables in Azure App Service:

```bash
# Required for Google Cloud Logging
GOOGLE_CLOUD_LOGGING_ENABLED=true
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_APPLICATION_CREDENTIALS=base64-encoded-service-account-json

# Optional configuration
GOOGLE_CLOUD_LOG_NAME=sleekflow-http-proxy-prod
GOOGLE_CLOUD_RESOURCE_TYPE=gce_instance
GOOGLE_CLOUD_ENHANCE_LOGS=true
GOOGLE_CLOUD_MAX_RETRIES=3
GOOGLE_CLOUD_TIMEOUT_MS=5000
```

#### Service Account Setup

1. Create a service account in Google Cloud Console
2. Grant the "Logging Writer" role to the service account
3. Generate a JSON key file for the service account
4. Base64 encode the JSON key file:
   ```bash
   base64 -w 0 service-account-key.json
   ```
5. Set the encoded string as `GOOGLE_APPLICATION_CREDENTIALS` environment variable

#### Health Monitoring

Monitor Google Cloud Logging status via actuator endpoints:

```bash
# Check overall health (includes Google Cloud Logging)
curl http://localhost:8080/actuator/health

# Check Google Cloud Logging specific status
curl http://localhost:8080/actuator/google-cloud-logging

# Test Google Cloud Logging connectivity
curl -X POST http://localhost:8080/actuator/google-cloud-logging/test

# Reinitialize Google Cloud Logging service
curl -X POST http://localhost:8080/actuator/google-cloud-logging/reinitialize
```

## Development

### Project Structure
```
src/
├── main/
│   ├── java/com/sleekflow/proxy/
│   │   ├── SleekFlowProxyApplication.java    # Main application class
│   │   ├── config/
│   │   │   ├── ProxyConfig.java              # WebClient configuration
│   │   │   └── ProxyProperties.java          # Configuration properties
│   │   ├── controller/
│   │   │   ├── ProxyController.java          # Main proxy controller
│   │   │   └── HealthController.java         # Health check endpoints
│   │   ├── service/
│   │   │   └── HttpForwardingService.java    # HTTP forwarding logic
│   │   ├── filter/
│   │   │   └── RequestLoggingFilter.java     # Request logging
│   │   └── exception/
│   │       └── GlobalExceptionHandler.java   # Error handling
│   └── resources/
│       └── application.properties            # Default configuration
└── test/                                     # Test classes
```

### Running Tests
```bash
mvn test
```

### Building for Production
```bash
mvn clean package -Dmaven.test.skip=true
```

## Troubleshooting

### Common Issues

1. **"Destination host is not configured"**
   - Ensure you set `proxy.destination.host` via command line, environment variable, or properties file

2. **Connection refused errors**
   - Verify the destination host is accessible
   - Check firewall settings
   - Ensure the destination URL is correct (include http:// or https://)

3. **Timeout errors**
   - Increase timeout values: `--proxy.connection-timeout-ms=60000`
   - Check network connectivity to destination

4. **Port already in use**
   - Change the listening port: `--proxy.server.port=8081`
   - Check if another service is using the port

### Debug Mode
Enable debug logging:
```bash
java -jar http-proxy-1.0.0.jar \
  --logging.level.com.sleekflow.proxy=DEBUG \
  --proxy.destination.host=http://httpbin.org
```

## Azure App Service Deployment

### Prerequisites

1. Azure App Service with Java 17 runtime
2. Google Cloud Project with Logging API enabled
3. Google Cloud Service Account with "Logging Writer" role

### Deployment Steps

1. **Build the application**:
   ```bash
   mvn clean package -DskipTests
   ```

2. **Deploy to Azure App Service** (using Azure CLI):
   ```bash
   az webapp deploy --resource-group <resource-group> \
     --name <app-service-name> \
     --src-path target/http-proxy-1.0.0.jar \
     --type jar
   ```

3. **Configure environment variables** in Azure App Service:
   ```bash
   # Proxy configuration
   az webapp config appsettings set --resource-group <resource-group> \
     --name <app-service-name> \
     --settings \
     PROXY_DESTINATION_HOST=https://your-api-endpoint.com \
     PROXY_SERVER_PORT=8080 \
     SPRING_PROFILES_ACTIVE=production

   # Google Cloud Logging configuration
   az webapp config appsettings set --resource-group <resource-group> \
     --name <app-service-name> \
     --settings \
     GOOGLE_CLOUD_LOGGING_ENABLED=true \
     GOOGLE_CLOUD_PROJECT=your-gcp-project-id \
     GOOGLE_APPLICATION_CREDENTIALS=<base64-encoded-service-account-json>
   ```

4. **Verify deployment**:
   ```bash
   # Check application health
   curl https://<app-service-name>.azurewebsites.net/actuator/health

   # Check Google Cloud Logging status
   curl https://<app-service-name>.azurewebsites.net/actuator/google-cloud-logging
   ```

### Environment Variables Reference

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `PROXY_DESTINATION_HOST` | Yes | - | Target API endpoint |
| `PROXY_SERVER_PORT` | No | 8080 | Application port |
| `SPRING_PROFILES_ACTIVE` | No | default | Spring profile (production, staging, development) |
| `GOOGLE_CLOUD_LOGGING_ENABLED` | No | false | Enable Google Cloud Logging |
| `GOOGLE_CLOUD_PROJECT` | No* | - | Google Cloud Project ID |
| `GOOGLE_APPLICATION_CREDENTIALS` | No* | - | Base64-encoded service account JSON |
| `GOOGLE_CLOUD_LOG_NAME` | No | sleekflow-http-proxy | Log name in Google Cloud |
| `GOOGLE_CLOUD_RESOURCE_TYPE` | No | global | Resource type for logs |

*Required when Google Cloud Logging is enabled

### Troubleshooting

#### Google Cloud Logging Issues

1. **Check health endpoint**:
   ```bash
   curl https://<app-service-name>.azurewebsites.net/actuator/health
   ```

2. **Verify configuration**:
   ```bash
   curl https://<app-service-name>.azurewebsites.net/actuator/google-cloud-logging
   ```

3. **Test connectivity**:
   ```bash
   curl -X POST https://<app-service-name>.azurewebsites.net/actuator/google-cloud-logging/test
   ```

4. **Common issues**:
   - **Invalid credentials**: Ensure Base64 encoding is correct and service account has proper permissions
   - **Network connectivity**: Check if Azure App Service can reach Google Cloud APIs
   - **Project ID mismatch**: Verify project ID matches the service account project
   - **API not enabled**: Ensure Cloud Logging API is enabled in Google Cloud Console

#### Application Stability

The application is designed to maintain stability even when Google Cloud Logging fails:
- Automatic fallback to console logging
- Non-blocking async logging operations
- Graceful error handling without application crashes
- Health checks to monitor logging service status

## License

This project is licensed under the MIT License.
