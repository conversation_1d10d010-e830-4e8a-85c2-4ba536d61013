# SleekFlow HTTP Proxy Server

A high-performance HTTP proxy/reverse proxy server built with Spring Boot 3 that forwards all incoming requests to a configurable destination server while preserving the complete original request.

## Features

- **Complete Request Preservation**: Forwards HTTP method, headers, body, query parameters, and authentication information
- **Protocol Support**: Supports both HTTP and HTTPS protocols
- **Configurable**: Destination hostname and listening port are fully configurable
- **Comprehensive Logging**: Detailed request tracking with correlation IDs
- **Error Handling**: Robust error handling for network failures and invalid requests
- **Health Monitoring**: Built-in health check endpoints
- **Performance**: Optimized with connection pooling and reactive programming

## Quick Start

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Build and Run

1. **Clone and build the project:**
   ```bash
   git clone <repository-url>
   cd SleekFlow.Proxy
   mvn clean package
   ```

2. **Run with default configuration:**
   ```bash
   java -jar target/http-proxy-1.0.0.jar --proxy.destination.host=http://httpbin.org
   ```

3. **Test the proxy:**
   ```bash
   curl http://localhost:8080/get
   # This will forward the request to http://httpbin.org/get
   ```

## Configuration

The proxy can be configured using multiple methods (in order of precedence):

### 1. Command Line Arguments
```bash
java -jar http-proxy-1.0.0.jar \
  --proxy.destination.host=http://api.example.com \
  --proxy.server.port=8080
```

### 2. Environment Variables
```bash
export PROXY_DESTINATION_HOST=http://api.example.com
export PROXY_SERVER_PORT=8080
java -jar http-proxy-1.0.0.jar
```

### 3. Application Properties
Edit `src/main/resources/application.properties`:
```properties
proxy.destination.host=http://api.example.com
proxy.server.port=8080
```

### Configuration Options

| Property | Environment Variable | Default | Description |
|----------|---------------------|---------|-------------|
| `proxy.destination.host` | `PROXY_DESTINATION_HOST` | `http://httpbin.org` | Destination server URL |
| `proxy.server.port` | `PROXY_SERVER_PORT` | `8080` | Proxy listening port |
| `proxy.connection-timeout-ms` | `PROXY_CONNECTION_TIMEOUT_MS` | `30000` | Connection timeout (ms) |
| `proxy.read-timeout-ms` | `PROXY_READ_TIMEOUT_MS` | `60000` | Read timeout (ms) |
| `proxy.follow-redirects` | `PROXY_FOLLOW_REDIRECTS` | `true` | Follow HTTP redirects |
| `proxy.max-redirects` | `PROXY_MAX_REDIRECTS` | `5` | Maximum redirects to follow |

## Usage Examples

### Basic HTTP Forwarding
```bash
# Start proxy forwarding to httpbin.org
java -jar http-proxy-1.0.0.jar --proxy.destination.host=http://httpbin.org

# Test GET request
curl http://localhost:8080/get

# Test POST request with JSON body
curl -X POST http://localhost:8080/post \
  -H "Content-Type: application/json" \
  -d '{"key": "value"}'
```

### API Gateway Scenario
```bash
# Forward to your API server
java -jar http-proxy-1.0.0.jar --proxy.destination.host=http://api.mycompany.com

# All requests are forwarded:
curl http://localhost:8080/api/users          # -> http://api.mycompany.com/api/users
curl http://localhost:8080/api/orders/123     # -> http://api.mycompany.com/api/orders/123
```

### HTTPS Destination
```bash
# Forward to HTTPS endpoint
java -jar http-proxy-1.0.0.jar --proxy.destination.host=https://api.github.com

curl http://localhost:8080/user              # -> https://api.github.com/user
```

## Monitoring and Health Checks

The proxy provides built-in monitoring endpoints:

### Health Check
```bash
curl http://localhost:8080/_proxy/health
```
Response:
```json
{
  "status": "UP",
  "timestamp": "2024-01-01T12:00:00Z",
  "uptime": 300000
}
```

### Detailed Status
```bash
curl http://localhost:8080/_proxy/status
```
Response:
```json
{
  "status": "UP",
  "timestamp": "2024-01-01T12:00:00Z",
  "startTime": "2024-01-01T11:55:00Z",
  "uptime": 300000,
  "configuration": {
    "destinationHost": "http://httpbin.org",
    "serverPort": 8080,
    "connectionTimeoutMs": 30000,
    "readTimeoutMs": 60000,
    "followRedirects": true,
    "maxRedirects": 5
  }
}
```

## Logging

The proxy provides comprehensive logging with correlation IDs for request tracking:

```
2024-01-01 12:00:00 - INCOMING REQUEST [abc-123] GET /api/users from *************
2024-01-01 12:00:00 - Forwarding GET request to: http://api.example.com/api/users
2024-01-01 12:00:01 - OUTGOING RESPONSE [abc-123] GET /api/users - Status: 200 - Duration: 150ms
```

### Log Levels
- `INFO`: Request/response summaries
- `DEBUG`: Detailed headers and body information
- `WARN`: Slow requests and error responses
- `ERROR`: Network failures and configuration errors

## Development

### Project Structure
```
src/
├── main/
│   ├── java/com/sleekflow/proxy/
│   │   ├── SleekFlowProxyApplication.java    # Main application class
│   │   ├── config/
│   │   │   ├── ProxyConfig.java              # WebClient configuration
│   │   │   └── ProxyProperties.java          # Configuration properties
│   │   ├── controller/
│   │   │   ├── ProxyController.java          # Main proxy controller
│   │   │   └── HealthController.java         # Health check endpoints
│   │   ├── service/
│   │   │   └── HttpForwardingService.java    # HTTP forwarding logic
│   │   ├── filter/
│   │   │   └── RequestLoggingFilter.java     # Request logging
│   │   └── exception/
│   │       └── GlobalExceptionHandler.java   # Error handling
│   └── resources/
│       └── application.properties            # Default configuration
└── test/                                     # Test classes
```

### Running Tests
```bash
mvn test
```

### Building for Production
```bash
mvn clean package -Dmaven.test.skip=true
```

## Troubleshooting

### Common Issues

1. **"Destination host is not configured"**
   - Ensure you set `proxy.destination.host` via command line, environment variable, or properties file

2. **Connection refused errors**
   - Verify the destination host is accessible
   - Check firewall settings
   - Ensure the destination URL is correct (include http:// or https://)

3. **Timeout errors**
   - Increase timeout values: `--proxy.connection-timeout-ms=60000`
   - Check network connectivity to destination

4. **Port already in use**
   - Change the listening port: `--proxy.server.port=8081`
   - Check if another service is using the port

### Debug Mode
Enable debug logging:
```bash
java -jar http-proxy-1.0.0.jar \
  --logging.level.com.sleekflow.proxy=DEBUG \
  --proxy.destination.host=http://httpbin.org
```

## License

This project is licensed under the MIT License.
