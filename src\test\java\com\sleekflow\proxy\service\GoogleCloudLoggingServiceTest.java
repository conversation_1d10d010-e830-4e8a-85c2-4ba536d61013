package com.sleekflow.proxy.service;

import com.sleekflow.proxy.config.GoogleCloudLoggingProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GoogleCloudLoggingServiceTest {

    @Mock
    private GoogleCloudCredentialsService credentialsService;

    private GoogleCloudLoggingService loggingService;
    private GoogleCloudLoggingProperties properties;

    @BeforeEach
    void setUp() {
        properties = new GoogleCloudLoggingProperties();
        loggingService = new GoogleCloudLoggingService(properties, credentialsService);
    }

    @Test
    void testInitialize_WhenDisabled() {
        // Given
        properties.setEnabled(false);

        // When
        loggingService.initialize();

        // Then
        assertFalse(loggingService.isHealthy());
        verify(credentialsService, never()).getCredentials();
    }

    @Test
    void testInitialize_WhenNoCredentials() {
        // Given
        properties.setEnabled(true);
        when(credentialsService.getCredentials()).thenReturn(null);
        when(credentialsService.getProjectId()).thenReturn(null);

        // When
        loggingService.initialize();

        // Then
        assertFalse(loggingService.isHealthy());
        assertNotNull(loggingService.getLastError());
    }

    @Test
    void testLogAsync_WhenUnhealthy_FallsBackToConsole() {
        // Given
        properties.setEnabled(true);
        // Service is unhealthy by default (not initialized)

        // When
        loggingService.logAsync("INFO", "Test message", null);

        // Then - should not throw exception and fall back to console
        assertDoesNotThrow(() -> {
            Thread.sleep(100); // Allow async operation to complete
        });
    }

    @Test
    void testLogAsync_WithCorrelationId() {
        // Given
        properties.setEnabled(true);
        MDC.put("correlationId", "test-correlation-123");

        try {
            // When
            loggingService.logAsync("INFO", "Test message with correlation", null);

            // Then - should not throw exception
            assertDoesNotThrow(() -> {
                Thread.sleep(100); // Allow async operation to complete
            });
        } finally {
            MDC.clear();
        }
    }

    @Test
    void testLogAsync_WithException() {
        // Given
        properties.setEnabled(true);
        RuntimeException testException = new RuntimeException("Test exception");

        // When
        loggingService.logAsync("ERROR", "Error message", testException);

        // Then - should not throw exception
        assertDoesNotThrow(() -> {
            Thread.sleep(100); // Allow async operation to complete
        });
    }

    @Test
    void testLogAsync_DifferentLogLevels() {
        // Given
        properties.setEnabled(true);

        // When & Then - should handle all log levels without throwing
        assertDoesNotThrow(() -> {
            loggingService.logAsync("ERROR", "Error message", null);
            loggingService.logAsync("WARN", "Warning message", null);
            loggingService.logAsync("INFO", "Info message", null);
            loggingService.logAsync("DEBUG", "Debug message", null);
            loggingService.logAsync("TRACE", "Trace message", null);
            loggingService.logAsync("UNKNOWN", "Unknown level message", null);
            
            Thread.sleep(200); // Allow async operations to complete
        });
    }

    @Test
    void testReinitialize() {
        // Given
        properties.setEnabled(true);
        when(credentialsService.getCredentials()).thenReturn(null);
        when(credentialsService.getProjectId()).thenReturn(null);

        // When
        loggingService.reinitialize();

        // Then
        verify(credentialsService, atLeastOnce()).getCredentials();
        verify(credentialsService, atLeastOnce()).getProjectId();
    }

    @Test
    void testShutdown() {
        // Given
        loggingService.initialize();

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> loggingService.shutdown());
    }

    @Test
    void testHealthStatus_InitiallyUnhealthy() {
        // When
        boolean healthy = loggingService.isHealthy();

        // Then
        assertFalse(healthy);
    }

    @Test
    void testPropertiesConfiguration() {
        // Given
        properties.setEnabled(true);
        properties.setProjectId("test-project");
        properties.setLogName("test-log");
        properties.setResourceType("test-resource");
        properties.setEnhanceLogEntries(true);
        properties.setMaxRetryAttempts(5);
        properties.setTimeoutMs(10000L);

        // When & Then
        assertEquals(true, properties.getEnabled());
        assertEquals("test-project", properties.getProjectId());
        assertEquals("test-log", properties.getLogName());
        assertEquals("test-resource", properties.getResourceType());
        assertEquals(true, properties.getEnhanceLogEntries());
        assertEquals(5, properties.getMaxRetryAttempts());
        assertEquals(10000L, properties.getTimeoutMs());
    }

    @Test
    void testPropertiesDefaults() {
        // When & Then
        assertEquals(false, properties.getEnabled());
        assertEquals("sleekflow-http-proxy", properties.getLogName());
        assertEquals("global", properties.getResourceType());
        assertEquals(true, properties.getEnhanceLogEntries());
        assertEquals(3, properties.getMaxRetryAttempts());
        assertEquals(5000L, properties.getTimeoutMs());
    }

    @Test
    void testPropertiesToString_RedactsCredentials() {
        // Given
        properties.setCredentialsBase64("sensitive-credentials");

        // When
        String toString = properties.toString();

        // Then
        assertFalse(toString.contains("sensitive-credentials"));
        assertTrue(toString.contains("[REDACTED]"));
    }

    @Test
    void testConcurrentLogging() {
        // Given
        properties.setEnabled(true);

        // When & Then - should handle concurrent logging without issues
        assertDoesNotThrow(() -> {
            for (int i = 0; i < 10; i++) {
                final int messageId = i;
                new Thread(() -> {
                    loggingService.logAsync("INFO", "Concurrent message " + messageId, null);
                }).start();
            }
            
            Thread.sleep(500); // Allow all async operations to complete
        });
    }
}
